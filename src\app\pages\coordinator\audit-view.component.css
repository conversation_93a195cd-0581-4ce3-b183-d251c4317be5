body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}
.month-view {
    display: none;
}

.width-300 {
    width: 300px;
}

.min-width-120 {
    min-width: 120px;
}


.wintable td,
.wintable th {
  padding: .5rem;
  min-width: 130px;
}

/* Monthly view specific header styling */
.outer .inner .wintable thead th {
  min-width: 80px; /* Reduce minimum width for monthly view */
  padding: 0.25rem; /* Reduce padding for tighter fit */
  text-align: center;
  vertical-align: middle;
}

/* Monthly view header text styling */
.outer .inner .wintable thead th .font-medium {
  font-size: 0.75rem !important; /* Smaller font for month/year */
  line-height: 1.1;
  margin: 0;
  padding: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.outer .inner .wintable thead th .font-xxx-large {
  font-size: 1.5rem !important; /* Reduce day number size */
  line-height: 1;
  margin: 0.25rem 0;
  padding: 0;
  font-weight: bold;
}

/* Ensure header content doesn't overflow */
.outer .inner .wintable thead th > div {
  margin: 0 !important;
  padding: 0.1rem 0;
  text-align: center;
  display: block;
  width: 100%;
}

/* Prevent text wrapping in headers */
.outer .inner .wintable thead th {
  white-space: nowrap;
  text-align: center !important;
}

/* Ensure weekday text is properly sized */
.outer .inner .wintable thead th .font-medium:last-child {
  font-size: 0.7rem !important;
  color: #ffffff;
  font-weight: normal;
}

.font-small {
    font-size: small;
}

.font-smaller {
    font-size: smaller;
}

.font-medium {
    font-size: medium;
}

.font-x-large {
    font-size: x-large;
}

.font-xxx-large {
    font-size: xxx-large;
}

.custom-date-range {
    width: 100%;
    min-height: calc(1.5em + .75rem + 5px);
    padding: .375rem .375rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.att-info-box {
    position: relative;
    padding-bottom: 15px !important;
}

.info-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    margin: 5px;
    background-color: #fff;
    border-radius: 50%;
}
.day-of-month{
    float: right;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.monthly-popup .table td {
    vertical-align: top !important;
    width: 133px;
    min-height: 113px;
}

.monthly-popup .att-info-box {
  position: unset;
  margin-top: 20px !important;
  display: inline-block;
  min-width: 100%;
  min-height: 90%;
}
.monthly-popup  .date-box{
    width: 100%;
}
.monthly-popup .date-label{
    right: 0px;
    top: 0px;
}
.outer .inner .wintable{
    width: 100% !important;
    table-layout: auto; /* Changed to auto for better column distribution */
    border-collapse: separate;
    border-spacing: 0;
    min-width: 100%; /* Ensure table takes full width */
  }

  /* Specific styling for monthly view columns */
  .outer .inner .wintable thead th:not(.fix) {
    width: auto;
    flex: 1;
    min-width: 80px;
    max-width: 120px;
  }

  /* Fixed first column - use sticky positioning for better visibility */
  .outer .inner .wintable tbody .fix {
    position: sticky !important;
    left: 0;
    width: 20vw;
    min-height: 93px;
    border: none;
    padding: 1px 2.5px;
    background-color: #fff; /* Ensure white background */
    z-index: 5; /* Above regular content but below header */
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1); /* Add subtle shadow for depth */
  }

  
  .day-body-col.fix{
    border-top: 1px solid #dee2e6 !important;
  }

  .inner {
    overflow-x: scroll;
    overflow-y: auto;
    width: 100%;
    max-height: 70vh; /* Set a maximum height to enable vertical scrolling */
    position: relative;
    scroll-behavior: smooth; /* Smooth scrolling for the container */
  }

  .cell-min-height{
    min-height: 96px;
  }

  /* Sticky header styles - enhanced for both week and month views */
  .outer .inner .wintable thead th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #6c757d !important; /* Ensure background color is maintained */
    border-bottom: 1px solid #dee2e6;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
    min-width: 80px; /* Consistent minimum width */
    max-width: 120px; /* Prevent headers from getting too wide */
    width: auto;
    overflow: hidden;
  }

  /* Special handling for the fixed first column header - keep it sticky but positioned correctly */
  .outer .inner .wintable thead th.fix {
    position: sticky !important; /* Override the absolute positioning for header only */
    top: 0;
    left: 0; /* Stick to the left edge */
    z-index: 11;
    background-color: #0169ab !important;
    width: 20vw; /* Match the original width */
    margin-left: 0; /* Reset margin for sticky positioning */
  }

  /* Ensure proper stacking context for scrollable content */
  .outer .inner .wintable tbody {
    position: relative;
    z-index: 1;
  }

  /* Ensure patient data is visible and properly styled */
  .outer .inner .wintable tbody .fix .row {
    margin: 0 !important;
  }

  .outer .inner .wintable tbody .fix .col-6 {
    padding: 0 5px;
  }

  /* Ensure the first column content is properly contained */
  .outer .inner .wintable tbody .fix {
    overflow: hidden;
    word-wrap: break-word;
  }

  /* Make sure the table cells have proper borders */
  .outer .inner .wintable tbody td {
    border: 1px solid #dee2e6;
    vertical-align: top;
  }

  /* Ensure the table container doesn't overflow */
  .outer {
    overflow: hidden;
  }

  /* Ensure patient buttons are properly styled and visible */
  .outer .inner .wintable tbody .fix .btn-link {
    color: #17a2b8 !important;
    text-decoration: none;
    padding: 0;
    border: none;
    background: none;
    text-align: left;
    width: 100%;
    white-space: normal;
    word-wrap: break-word;
  }

  .outer .inner .wintable tbody .fix .btn-link:hover {
    color: #138496 !important;
    text-decoration: underline;
  }



  /* Ensure monthly popup is not affected by sticky header styles */
  .monthly-popup .table thead th {
    position: static !important;
    z-index: auto !important;
    box-shadow: none !important;
  }

  .monthly-popup .table {
    table-layout: auto !important;
    border-collapse: collapse !important;
  }


@media (min-width: 577px) {
    .container {
        width: 100%;
        max-width: unset;
        padding: 0 30px;
    }
    .outer {
        position: relative;
        background-color: #fff;
        width: 100%;
      }

    /* Desktop-specific monthly view header adjustments */
    .outer .inner .wintable thead th {
        min-width: 90px;
        max-width: 130px;
        padding: 0.3rem;
    }

    .outer .inner .wintable thead th .font-medium {
        font-size: 0.8rem !important;
    }

    .outer .inner .wintable thead th .font-xxx-large {
        font-size: 1.8rem !important;
        margin: 0.3rem 0;
    }
}

@media (max-width: 576px) {
    .min-width-80 {
        min-width: 80px;
    }

    .filter-icon-wrapper {
        fill: white;
    }

    .container {
        padding: 0;
    }

    /* Mobile-specific adjustments for sticky header */
    .inner {
        max-height: 60vh; /* Slightly smaller on mobile */
    }

    /* Mobile adjustments for fixed column */
    .outer .inner .wintable tbody .fix {
        width: 30vw; /* Wider on mobile for better readability */
        font-size: 0.8rem;
    }

    .outer .inner .wintable thead th.fix {
        width: 30vw; /* Match body column width */
    }

    /* Adjust other columns on mobile */
    .wintable td,
    .wintable th {
        min-width: 60px; /* Much smaller minimum width on mobile */
        padding: 0.2rem; /* Reduce padding on mobile */
    }

    /* Mobile-specific monthly view header adjustments */
    .outer .inner .wintable thead th .font-medium {
        font-size: 0.6rem !important; /* Even smaller font on mobile */
        line-height: 1;
    }

    .outer .inner .wintable thead th .font-xxx-large {
        font-size: 1.2rem !important; /* Smaller day numbers on mobile */
        line-height: 1;
        margin: 0.1rem 0;
    }

    /* Ensure mobile headers don't overflow */
    .outer .inner .wintable thead th {
        min-width: 50px !important;
        max-width: 70px !important;
        padding: 0.1rem !important;
    }

    .carousel-inner {
        position: relative;
        overflow: hidden;
    }

    .carousel-item {
        width: 90%;
        margin: auto;
        text-align: center;
        float: none;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 5%;
        opacity: 1;
        background: transparent;
    }

    .carousel-control-prev-icon {
        background-image: url(../../../assets/left-arrow-next.svg);
    }

    .carousel-control-next-icon {
        background-image: url(../../../assets/right-arrow-next.svg);
    }

    #myCarousel {
        overflow: hidden;
    }

    .card-head,
    .card-body {
        border: 1px solid #fff;
    }

    .close-button {
        float: right;
        margin-top: 0px;
        margin-right: 10px;
    }

    .close-button img {
        width: 15px;
    }  
    
}
