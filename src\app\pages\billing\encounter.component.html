<!-- card ends -->
<div *ngFor="let mainItem of lisfOfEncounters; let i= index;" id="accordion-{{i}}" class="p-2 bg-card{{i}} mb-2">
    <div class="row mx-auto">
        <div class="col-12 col-md-6 px-0">
            <h6 class="font-weight-bold text-dark font-size-large">{{mainItem.groupName}}
                <a *ngIf="mainItem.listOfEncounters[0].isAlertCheck"
                    (click)="getAlerts(PatientObject,mainItem.groupName,mainItem.listOfEncounters)">
                    <img alt=' ' src="../../../assets/img/alerts.gif" width="20px" height="20px" class="mx-1" />
                </a>
            </h6>
        </div>
        <div class="col-12 col-md-6 ml-auto text-right">
            <ng-container *ngIf="mainItem.marAsUnBilled>0&&mainItem.allCount>1">
                <a class="mx-1" (click)="markAsAllBilled(mainItem.listOfEncounters,PatientObject)">
                    <img alt=' ' src="../../../assets/img/markpin.png" title="Mark all as Billed" width="26px"
                        data-target="#markAsAllBilled" data-toggle="modal">
                </a>
            </ng-container>
            <ng-container *ngIf="(mainItem.isEnvSendAllToKareo>0||mainItem.isEnvSendAllToExistingKareo>0)">
                <a class="sendTokareo ml-3 cursor-pointer"><img alt=' ' [src]="img7" class="pb-1" style="height:30px"
                        data-target="#finalizeICdsAll" data-toggle="modal" title="Send Encounter(s) to Kareo"
                        (keyup)="finalizeICdsAll(mainItem.listOfEncounters)"
                        (click)="finalizeICdsAll(mainItem.listOfEncounters)"></a>
            </ng-container>
            <ng-container *ngIf="(mainItem.isEnvSendAllToRXNT>0||mainItem.isEnvSendAllToExistingRXNT>0)">
                <a class="sendTokareo ml-3">
                    <img alt=' ' [src]="img4" class="pb-1" style="height:30px" data-target="#finalizeRXNTICdsAll"
                        data-toggle="modal" title="Send Encounter(s) to RXNT"
                        (keyup)="finalizeRXNTICdsAll(mainItem.listOfEncounters)"
                        (click)="finalizeRXNTICdsAll(mainItem.listOfEncounters)"></a>
            </ng-container>
            <ng-container>
                <a *ngIf="(mainItem.isEnvSendAllToADMD>0||mainItem.isEnvSendAllToExistingADMD>0)"
                    class="sendTokareo ml-3"><img alt=' ' [src]="img5" class="pb-1" style="height:30px"
                        data-target="#finalizeADMDICdsAll" data-toggle="modal" title="Send Encounter(s) to ADMD"
                        (keyup)="finalizeADMDICdsAll(mainItem.listOfEncounters)"
                        (click)="finalizeADMDICdsAll(mainItem.listOfEncounters)"></a>
            </ng-container>
        </div>
    </div>

    <div class="card" *ngFor="let item of mainItem.listOfEncounters">
        <ng-container *ngIf="item.encounteR_ID!=''">
            <div class="card-header-new py-0 px-0 bill-inner position-relative" id="billing1-{{item.encounteR_ID}}"
                style="text-decoration: line-through;">
                <h5 class="mb-0">
                    <button class="btn btn-link bill-arrow text-white  collapsed align-items-center d-flex float-left"
                        data-toggle="collapse" attr.data-target="#bills-{{item.encounteR_ID}}" aria-expanded="true"
                        attr.aria-controls="bills-{{i}}">

                        <span class="pl-md-4" [class.strike]="item.status=='1'">
                            <i>Encounter created at:</i>
                            {{item.encounterseendate}}--<span class="small">
                                <i>By <span class="text-color-green">{{item.posT_TO_BILLED_BY}}</span></i>
                            </span>
                        </span>
                        <span class="ml-1">[</span>
                        <div tooltip="{{convertCPTCodes(item.listOfCpts)}}" triggers="mouseenter mouseleave click"
                            class="w-fit-content">
                            <span>{{getTwoCPTCodes(item.listOfCpts)}}</span>
                        </div>
                        <span>]</span>
                        <span class="sub-group">{{item.subGroupName}}</span>
                        <span class="ml-2"  style="color:#b2d6cd;font-size:12px"
                            *ngIf="item.lastmodifiedby&&item.lastmodifieddate">
                            * Modified By :{{item.lastmodifiedby}} on
                            {{item.lastmodifieddate}}
                        </span>
                    </button>

                    <div class="dropdown no-arrow pl-3 py-0 pr-0 position-absolute no_top_right bill-inner">
                        <a *ngIf="item.status !='1' &&  mainItem.listOfEncounters.length > 1" data-dismiss="modal"
                            (click)="submitICDsMultiEcounters(item,item.encounteR_ID,PatientObject,mainItem.listOfEncounters)"
                            title="Replace ICD's for All Encounters" class="mr-1"><i
                                class="fas fa-window-restore fa-1x  p-1 text-white small" style="height:22px"></i></a>
                        <!-- kareo button template starts -->
                        <ng-container
                            *ngIf="item.status=='1'&&item.integrationSystem=='Kareo' && item.existing_Kareo_ID>0;else exKareoTemp">
                            <ng-container
                                *ngIf="item.status=='1'&&item.integrationSystem=='Kareo'&&item.existing_Kareo_Batch_No==0;else exKareoTempElse">
                                <a (click)="finalizeICDs(item.encounteR_ID,mainItem.listOfEncounters)">
                                    <img alt=' ' [src]="img7" class="pb-1" style="height:22px"
                                        data-target="#finalizeICdsAll" data-toggle="modal"
                                        title="Send Encounter(s) to Kareo">
                                </a>
                            </ng-container>
                            <ng-template #exKareoTempElse>
                                <a *ngIf="item.status=='1'&&item.integrationSystem=='Kareo'"
                                    id="imgSentKareo-{{item.encounteR_ID}}">
                                    <img alt=' ' src="../../../assets/img/Kareo_Logo-yash.png" class="pb-1"
                                        style="height:22px" title="Sent to Kareo">
                                </a>
                            </ng-template>
                        </ng-container>
                        <ng-template #exKareoTemp>
                            <ng-container
                                *ngIf="item.status=='1'&&item.integrationSystem=='Kareo'&&item.kareo_ID>0&&item.kareo_Batch_No==0;else kareoTempElse">
                                <a (click)="finalizeICDs(item.encounteR_ID,mainItem.listOfEncounters)">
                                    <img alt=' ' [src]="img7" class="pb-1" style="height:22px"
                                        data-target="#finalizeICdsAll" data-toggle="modal"
                                        title="Send Encounter(s) to Kareo">
                                </a>
                            </ng-container>
                            <ng-template #kareoTempElse>
                                <a *ngIf="item.status=='1'&&item.integrationSystem=='Kareo'"
                                    id="imgSentKareo-{{item.encounteR_ID}}">
                                    <img alt=' ' src="../../../assets/img/Kareo_Logo-yash.png" class="pb-1"
                                        style="height:22px" title="Sent to Kareo">
                                </a>
                            </ng-template>
                        </ng-template>
                        <!-- kareo button template ends -->
                        <!-- RXNT button template starts -->
                        <ng-container
                            *ngIf="item.status=='1'&&item.integrationSystem=='RXNT' && item.existing_RXNT_ID>0;else exRXNTTemp">
                            <ng-container
                                *ngIf="item.status=='1'&&item.integrationSystem=='RXNT' && item.existing_RXNT_Batch_No==0;else exRXNTTempElse">
                                <a (click)="finalizeRXNTICDs(item.encounteR_ID,mainItem.listOfEncounters)">
                                    <img alt=' ' [src]="img4" class="pb-1" style="height:22px"
                                        data-target="#finalizeRXNTICdsAll" data-toggle="modal"
                                        title="Send Encounter(s) to RXNT">
                                </a>
                            </ng-container>
                            <ng-template #exRXNTTempElse>
                                <a *ngIf="item.status=='1'&&item.integrationSystem=='RXNT'"
                                    id="imgSentRXNT-{{item.encounteR_ID}}">
                                    <img alt=' ' [src]="img4" class="pb-1" style="height:22px;filter: grayscale(100%);"
                                        title="Sent to RXNT">
                                </a>
                            </ng-template>
                        </ng-container>
                        <ng-template #exRXNTTemp>
                            <ng-container
                                *ngIf="item.status=='1'&&item.integrationSystem=='RXNT'  && item.RXNT_ID>0&&item.RXNT_Batch_No==0;else RXNTTempElse">
                                <a (click)="finalizeRXNTICDs(item.encounteR_ID,mainItem.listOfEncounters)">
                                    <img alt=' ' [src]="img4" class="pb-1" style="height:22px"
                                        data-target="#finalizeRXNTICdsAll" data-toggle="modal"
                                        title="Send Encounter(s) to RXNT">
                                </a>
                            </ng-container>
                            <ng-template #RXNTTempElse>
                                <a *ngIf="item.status=='1'&&item.integrationSystem=='RXNT' "
                                    id="imgSentRXNT-{{item.encounteR_ID}}">
                                    <img alt=' ' [src]="img4" class="pb-1" style="height:22px;filter: grayscale(100%);"
                                        title="Sent to RXNT">
                                </a>
                            </ng-template>
                        </ng-template>
                        <!-- RXNT button template ends -->
                        <!-- ADMD button template starts -->
                        <ng-container
                            *ngIf="item.status=='1'&& item.integrationSystem=='ADMD' && item.existing_ADMD_ID >0 ;else exADMDTemp">
                            <ng-container
                                *ngIf="item.status=='1'&&item.integrationSystem=='ADMD' && item.existing_ADMD_Batch_No==0;else exADMDTempElse">
                                <a (click)="finalizeADMDICDs(item.encounteR_ID,mainItem.listOfEncounters)">
                                    <img alt=' ' [src]="img5" class="pb-1" style="height:22px"
                                        data-target="#finalizeADMDICdsAll" data-toggle="modal"
                                        title="Send Encounter(s) to ADMD">
                                </a>
                            </ng-container>
                            <ng-template #exADMDTempElse>
                                <a *ngIf="item.status=='1'&&item.integrationSystem=='ADMD'"
                                    id="imgSentADMD-{{item.encounteR_ID}}">
                                    <img alt=' ' [src]="img5" class="pb-1" style="height:22px;filter: grayscale(100%);"
                                        title="Sent to ADMD">
                                </a>
                            </ng-template>
                        </ng-container>
                        <ng-template #exADMDTemp>
                            <ng-container
                                *ngIf="item.status=='1'&&item.integrationSystem=='ADMD' && item.ADMD_ID>0&&item.ADMD_Batch_No==0;else ADMDTempElse">
                                <a (click)="finalizeADMDICDs(item.encounteR_ID,mainItem.listOfEncounters)">
                                    <img alt=' ' [src]="img5" class="pb-1" style="height:22px"
                                        data-target="#finalizeADMDICdsAll" data-toggle="modal"
                                        title="Send Encounter(s) to ADMD">
                                </a>
                            </ng-container>
                            <ng-template #ADMDTempElse>
                                <a *ngIf="item.status=='1'&&item.integrationSystem=='ADMD'"
                                    id="imgSentADMD-{{item.encounteR_ID}}">
                                    <img alt=' ' [src]="img5" class="pb-1" style="height:22px;filter: grayscale(100%);"
                                        title="Sent to ADMD">
                                </a>
                            </ng-template>
                        </ng-template>
                        <!-- ADMD button template ends -->


                        <a (click)="saveChanges(item)" style="display:none" class="btnSave{{item.encounteR_ID}} mx-1">
                            <img alt=' ' src="../../../assets/img/save.png" title="Save Edit Charges">
                        </a>

                        <a class="dropdown-toggle" id="dropdownMenuLink-{{item.encounteR_ID}}" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                            <i class="fa-ellipsis-v fa-2x fas fa-fw fa-sm fas pt-1 text-white-50"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated-fade-in"
                            attr.aria-labelledby="dropdownMenuLink-{{item.encounteR_ID}}">
                            <a class="dropdown-item deletepointer" data-target="#updateSeenDate" data-toggle="modal"
                                (click)="editEncounterSeenDate(item,PatientObject)">Edit Encounter Seen Date</a>
                            <a *ngIf="item.status=='0'" class="dropdown-item deletepointer" data-target="#markAsBilled"
                                data-toggle="modal" (click)="markAsBilled(item,PatientObject)">Mark as Billed</a>
                            <a *ngIf="item.status=='0' && item.integrationSystem=='NG'"
                                class="dropdown-item deletepointer" data-target="#markAsAlreadyBilled"
                                data-toggle="modal" (click)="markAsBilled(item,PatientObject)">Mark as already
                                Billed</a>
                            <a *ngIf="item.status=='1'" class="dropdown-item deletepointer"
                                data-target="#markAsUnBilled" data-toggle="modal"
                                (click)="markAsUnBilled(item,PatientObject)">Mark as Unbilled</a>
                            <a class="dropdown-item deletepointer" data-target="#viewHistory" data-toggle="modal"
                                (click)="viewHistory(item.encounteR_ID)">View Edit History</a>
                            <a *ngIf="item.status=='0'" class="dropdown-item deletepointer"
                                data-target="#deleteEncounter" data-toggle="modal"
                                (click)="deleteEncounter(item)">Delete Encounter</a>
                        </div>
                    </div>
                </h5>
            </div>

            <div id="bills-{{item.encounteR_ID}}"
                [ngClass]="{'show isFirstClass' :item.encounteR_ID==firstEcounterIdToOpen}" class="collapse"
                attr.aria-labelledby="billing1-{{item.encounteR_ID}}" attr.data-parent="#accordion-{{i}}">
                <div class="card-body p-2 p-md-4">
                    <!-- inner accordion 1-->
                    <div id="accordion_inner-{{item.encounteR_ID}}" class="mb-2">
                        <div class="card">
                            <div class="card-header-new py-0 px-0 dan-blue" id="billing1-{{item.encounteR_ID}}">
                                <h5 class="mb-0 position-relative">
                                    <button
                                        class="btn btn-link bill-arrow collapsed align-items-center d-flex text-white"
                                        data-toggle="collapse" attr.data-target="#bills_inner-{{item.encounteR_ID}}"
                                        aria-expanded="true" attr.aria-controls="bills_inner-{{item.encounteR_ID}}">
                                        <span class="pl-md-4 pl-2">CPT/HCPCS Codes</span>
                                    </button>
                                    <a *ngIf="item.status=='0'" (click)="getCPTData(item,item.physicianmailid,'Add')"
                                        class="float-right position-absolute top-right-10 blld-{{item.encounteR_ID}}"
                                        data-toggle="modal" data-target="#CPTData"><i
                                            class="fas fa-plus-circle fa-2x "></i></a>
                                </h5>
                            </div>

                            <div id="bills_inner-{{item.encounteR_ID}}"
                                [ngClass]="{'show isFirstClass' :item.encounteR_ID==firstEcounterIdToOpen}"
                                class="collapse" attr.aria-labelledby="bills_inner-{{item.encounteR_ID}}"
                                attr.data-parent="#accordion_inner-{{item.encounteR_ID}}">
                                <div class="card-body" id="cptDiv-{{item.encounteR_ID}}">
                                    <div class="row" *ngFor="let cpt of item.listOfCpts">
                                        <div *ngIf="item.status=='0'">
                                            <span class="mx-1 edit-ancor" data-toggle="modal" data-target="#CPTData"
                                                (keyup)="getCPTData(item,item.physicianmailid,cpt)"
                                                (click)="getCPTData(item,item.physicianmailid,cpt)">
                                                <i class="fas fa-edit" title="Edit"></i>
                                            </span>
                                            <span class="mx-1 edit-ancor" (keyup)="deleteCPTCode(item,cpt)"
                                                (click)="deleteCPTCode(item,cpt)">
                                                <i class="far fa-trash-alt" title="Remove"></i>
                                            </span>
                                        </div>
                                        <div class="col-9">
                                            <span class="cpt-colors" [class.strike]="item.status=='1'">
                                                {{cpt}}</span>
                                        </div>
                                        <div *ngIf="item.status=='0'">
                                            <a href="#" class="mx-1" data-toggle="modal" data-target="#ModifierData"
                                                (click)="getModifierData(item,cpt)">
                                                Add/Modify Modifiers</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- inner accordion end 1-->
                    <!-- inner accordion 2-->
                    <div id="accordion_inner1-{{item.encounteR_ID}}" class="mb-2">
                        <div class="card">
                            <div class="card-header-new py-0 px-0" id="billing2-{{item.encounteR_ID}}">
                                <h5 class="mb-0 position-relative dan-blue">
                                    <button
                                        class="btn btn-link bill-arrow  collapsed align-items-center d-flex text-white"
                                        data-toggle="collapse" attr.data-target="#bills_inner1-{{item.encounteR_ID}}"
                                        aria-expanded="true" attr.aria-controls="bills_inner1-{{item.encounteR_ID}}">
                                        <span class="pl-md-4 pl-2">ICD Codes</span>
                                    </button>
                                    <a *ngIf="item.status=='0'" (click)="getICDData(item,item.physicianmailid,'Add')"
                                        class="float-right position-absolute top-right-10 blld-{{item.encounteR_ID}}"
                                        data-toggle="modal" data-target="#ICDData"><i
                                            class="fas fa-plus-circle fa-2x "></i></a>

                                </h5>
                            </div>

                            <div id="bills_inner1-{{item.encounteR_ID}}"
                                [ngClass]="{'show isFirstClass' :item.encounteR_ID==firstEcounterIdToOpen}"
                                class="collapse" attr.aria-labelledby="bills_inner1-{{item.encounteR_ID}}"
                                attr.data-parent="#accordion_inner1-{{item.encounteR_ID}}">
                                <div class="card-body col-11 float-left">
                                    <div class="selectrow" *ngFor="let icd of item.listOfIcds;let i = index"
                                        [class.active]="i == HighlightRow">
                                        <div *ngIf="item.status=='0'" class="blld-{{item.encounteR_ID}}">
                                            <span class="mx-1 edit-ancor" data-toggle="modal" data-target="#ICDData"
                                                (keyup)="getICDData(item,item.physicianmailid,icd)"
                                                (click)="getICDData(item,item.physicianmailid,icd)">
                                                <i class="fas fa-edit" title="Edit"></i>
                                            </span>
                                            <span class="mx-1 edit-ancor" (click)="deleteICDCode(item,icd)"
                                                (keyup)="deleteICDCode(item,icd)">
                                                <i class="far fa-trash-alt" title="Remove"></i>
                                            </span>
                                        </div>
                                        <div class="col-8">
                                            <span class="cpt-colors" [class.strike]="item.status=='1'"(keyup)="ClickedRow(i)" (click)="ClickedRow(i)">{{icd}}</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="card-body col-1 float-lg-left text-center vertical-align-middle">
                                    <img id="up" [src]='img1' alt="uparrow" (keyup)="moveSelectedUp(item)"
                                        (click)="moveSelectedUp(item)" title="Up" class="cursor-pointer pr-1 up-arrow">
                                    <img id="down" [src]='img2' alt="downarrow" (keyup)="moveSelectedDown(item)"
                                        (click)="moveSelectedDown(item)" title="Down" class="cursor-pointer down-arrow">
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- inner accordion end 2-->
                </div>

            </div>


        </ng-container>
    </div>

</div>

<app-replace-icdcodes [listOfAllEncounters]="lisfOfAllEncounters" [listOfIcds]="listOfIcds"
    [listOfRemovedCpts]="listOfRemovedCpts" [listOfRemovedIcds]="listOfRemovedIcds" [PatientObject]="PatientObject"
    [encounterObj]="encounterObj" (eventGetEncountersByPatient)="getEncountersByPatient(PatientObject)"
    [userType]="'Biller'"></app-replace-icdcodes>
<!-- card starts -->
<!--Confirmation for submit ICD's for multiple encounters-->

<!-- CPT Codes popup starts -->
<app-cpt-code [lisfOfCPTData]="lisfOfCPTData" [cptType]="cptType" [encounterObj]="encounterObj"
    (eventListOfRemovedCpts)="updateRemovedCptList(encounterObj)" (eventUpdateUpdatedCptDataList)="updateUpdatedCptDataList(encounterObj)"></app-cpt-code>
<!-- CPT Codes popup ends -->

<!-- ICD Codes popup starts -->
<app-icd-code [lisfOfICDData]="lisfOfICDData" [icdType]="icdType" [encounterObj]="encounterObj"
    (isICDforMultiencounter)="getICDChange($event)"
    (eventListOfRemovedIcds)="updateRemovedIcdList(encounterObj)" (eventUpdateUpdatedICDDataList)="updateUpdatedICDDataList(encounterObj)"></app-icd-code>
<!-- ICD Codes popup end -->

<!-- Modifier popup starts -->
<app-cpt-modifiers [listOfModifier]="listOfModifier" [encounterObj]="encounterObj" [cptType]="cptType"></app-cpt-modifiers>
<!-- Modifier popup end -->

<!-- Cpt Modifier popup starts -->
<app-remove-reasons-cptcomponent [lisfOfCPTData]="lisfOfCPTData" [cptCode]="cptCode"
    (eventRemoveReason)="updateDeleteReason($event)"
    (eventCancelRemoveReason)="updateCancelDeleteReason($event)"></app-remove-reasons-cptcomponent>
<!-- Cpt Modifier popup end -->