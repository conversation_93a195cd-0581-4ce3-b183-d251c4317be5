import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { apiUrl } from 'src/app/config';
import { Guid } from "guid-typescript";
import { EncrDecrServiceService } from './encr-decr-service.service';
import { DataService } from './data.service';
import { Observable } from 'rxjs';
declare let $: any;

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  public CustomSessionID: Guid;
  constructor(private readonly http: HttpClient, private readonly encrDecr: EncrDecrServiceService, private readonly data: DataService) { }

  startLoading() {
    $("#loading").show();
  }

  stopLoading() {
    $("#loading").hide();
  }

  getUserAccess() {
    this.CustomSessionID = Guid.create();
    let encryptedCustomSessionID = this.encrDecr.set(this.CustomSessionID);
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json', 'GRSessionId': encryptedCustomSessionID });
    return this.http.get(apiUrl + 'api/Common/GetUserAccess', { headers: headers });
  }

  logout() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Common/GRLogout', { headers: headers });
  }

  getFacilities() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Common/GetFacilities', { headers: headers });
  }

  getFacilitiesForKareo() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Common/GetFacilities/Kareo', { headers: headers });
  }

  getPhysiciansByFacility(facillity) {
    const body = { FacilityName: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetPhysicians', body, { headers: headers });
  }

  getPhysiciansByUserType(facillity, userType) {
    const body = { FacilityName: this.encrDecr.set(facillity), UserType: this.encrDecr.set(userType) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetPhysiciansByUserType', body, { headers: headers });
  }

  getCPTData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetCPTData', request, { headers: headers });
  }

  getICDData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetICDData', request, { headers: headers });
  }

  getAttachments(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/FileUpload/GetAttachments', request, { headers: headers });
  }

  getMissingEncounters(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetPatientMissingEncounterByPatient', request, { headers: headers });
  }

  insertMissingEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/InsertMissingEncounter', request, { headers: headers });
  }

  getUserGroups(facillity) {
    const body = { ParamOne: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetUserGroups', body, { headers: headers });
  }

  insertCPTData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/InsertCPTData', request, { headers: headers });
  }

  searchCPTData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/AsyncGetSearchCPTData', request, { headers: headers });
  }

  insertICDData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/InsertICDData', request, { headers: headers });
  }

  searchICDData(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/AsyncGetSearchICDData', request, { headers: headers });
  }

  getNotes(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetNotes', request, { headers: headers });
  }

  insertNotes(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/InsertNotes', request, { headers: headers });
  }

  updateEncounterDate(sTimeZone) {
    const body = { FacilityName: this.encrDecr.set(sTimeZone) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/UpdateEncounterDate', body, { headers: headers });
  }

  getEncouterHistory(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetEncouterHistoryNew', request, { headers: headers });
  }

  downloadAttachment(fileName) {
    const body = { FacilityName: this.encrDecr.set(fileName) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/FileUpload/DownloadAttachment', body, { headers: headers });
  }

  deleteAttachment(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/DeleteAttachment', request, { headers: headers });
  }

  uploadFiles(formData) {
    return this.http.post(apiUrl + 'api/AzureBlob/UploadFile', formData, { reportProgress: true });
  }

  getPatientDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetPatientDetails', request, { headers: headers });
  }

  getFacilitiesByUserType(userType) {
    const body = { ParamOne: this.encrDecr.set(userType) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetFacilitiesByUserType', body, { headers: headers });
  }
  GetFacilitiesByCoorORAudr(userType) {
    const body = { ParamOne: this.encrDecr.set(userType) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetFacilitiesByCoorORAudr', body, { headers: headers });
  }

  DeleteMissingEncounter(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/DeleteMissingEncounter', request, { headers: headers });
  }

  getDepartmentsByFacility(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetDepartmentsByFacility', request, { headers: headers });
  }

  GetGroupsByFacility(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetGroupsByFacility', request, { headers: headers });
  }

  getPatientInfo(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetPatientInfo', request, { headers: headers });
  }
  extractMSALToken() {
    const timestamp = Math.floor((new Date()).getTime() / 1000);
    let token = null;
    for (const key of Object.keys(localStorage)) {
      if (key.includes('login.windows.net-accesstoken') && key.includes('user.read')) {
        const val: any = JSON.parse(localStorage.getItem(key)!);
        if (val.expiresOn) {
          // We have a (possibly expired) token

          if (val.expiresOn > timestamp && val.secret) {
            // Found the correct token
            token = val.secret;
          } else {
            console.log('will remove ' + key);
            this.data.updateLogginStatus(false);
            // Clear old data
            localStorage.removeItem(key);
            this.clearExpiryData();
          }
        }
      }
    }
    if (token) { return token; } else { return null; }
  }

  clearExpiryData() {
    for (const key of Object.keys(localStorage)) {
      if (key.includes('login.windows.net') || key.includes('ng2Idle.main') || key.includes('lastPing')) {
        console.log('will remove ' + key);
        // Clear old data
        localStorage.removeItem(key);
      }
    }
  }

  UnDischargePatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/UnDischargePatient', request, { headers: headers });
  }

  getADUsers(searched_key):Observable<any> {
    const body = { searched_key: this.encrDecr.set(searched_key) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/getADUsers', body, { headers: headers });
  }

  insertPatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/InsertPatient', request, { headers: headers });
  }

  updatePatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/UpdatePatient', request, { headers: headers });
  }

  getFacilitiesDetails() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/ViewFacilitiesDetails', null, { headers: headers });
  }

  getPhysiciansByFacilityForReports(facillity) {
    const body = { FacilityName: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetPhysiciansForReports', body, { headers: headers });
  }

  updatePrimePatient(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/UpdatePrimePatient', request, { headers: headers });
  }
  AssignmentUploadFiles(formData) {
    return this.http.post(apiUrl + 'api/AzureBlob/AssignmentUploadFile', formData, { reportProgress: true });
  }
  getFacilitiesForNG() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Common/GetFacilities/NG', { headers: headers });
  }
  getDeletedEncountersDetails(request) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/AsyncGetDeletedEncountersByPatient', request, { headers: headers });
  }
  deleteEncounter(request, userType) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    if (userType == 'BILLER') {
      return this.http.post(apiUrl + 'api/Billing/DeleteEncounter', request, { headers: headers });
    } else {
      return this.http.post(apiUrl + 'api/Coordinator/DeleteEncounter', request, { headers: headers });
    }
  }
  updateDischargeDate(request, userType) {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    if (userType == 'BILLER') {
      return this.http.post(apiUrl + 'api/Billing/UpdateDischargeDate', request, { headers: headers });
    } else if (userType == 'COORDINATOR') {
      return this.http.post(apiUrl + 'api/Coordinator/UpdateDischargeDate', request, { headers: headers });
    } else {
      return this.http.post(apiUrl + 'api', request, { headers: headers });
    }
  }
  checkForUpdates() {
    return this.http.get('assets/timestamp.txt', { responseType: 'text' });
  }
  getFacilitiesForEPIC() {
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.get(apiUrl + 'api/Common/GetFacilities/EPIC', { headers: headers });
  }
  getGroupNameByFacilityAndRole(facillity) {
    const body = { FacilityName: this.encrDecr.set(facillity) };
    let headers = new HttpHeaders({ 'Content-Type': 'application/json', 'Accept': 'application/json' });
    return this.http.post(apiUrl + 'api/Common/GetGroupNameByFacilityAndRole', body, { headers: headers });
  }

}
