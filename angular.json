{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"PrimeGrandRoundsAngularApp": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/PrimeGrandRoundsAngularApp", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css", "src/assets/css/jquery-1.8.9-ui.css", "node_modules/ngx-toastr/toastr.css", "node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "node_modules/@mobiscroll/angular-lite/dist/css/mobiscroll.min.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/jquery/dist/jquery.min.js", "src/assets/js/bootstrap.bundle.min.js", "src/assets/js/Jquery_ui_min.js", "src/assets/js/ui_timepicker.js", "node_modules/popper.js/dist/umd/popper.min.js", "node_modules/crypto-js/crypto-js.js", "src/assets/js/chosen.jquery.js", "src/assets/js/init.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "7mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "6kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "PrimeGrandRoundsAngularApp:build:production"}, "development": {"browserTarget": "PrimeGrandRoundsAngularApp:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "PrimeGrandRoundsAngularApp:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.css"], "scripts": []}}}}}}